#!/usr/bin/env python3
"""
🧪 ENHANCED INTEGRATION TESTS
Testy integracji między Python Mixer a GoBackend-Kratos
Sprawdza stabilno<PERSON>ć, ni<PERSON><PERSON><PERSON><PERSON><PERSON> i wydajność integracji
"""

import asyncio
import pytest
import aiohttp
import json
import time
from datetime import datetime
from typing import Dict, Any
import logging

from python_mixer.core.integration.resilience_manager import resilience_manager, CircuitBreakerConfig, RetryConfig
from python_mixer.core.integration.data_flow_orchestrator import (
    data_flow_orchestrator, 
    DataFlowMessage, 
    DataFlowType, 
    DataPriority
)
from python_mixer.core.integration.unified_bridge import integration_bridge

logger = logging.getLogger(__name__)

class TestEnhancedIntegration:
    """🧪 Testy wzmocnionej integracji"""
    
    @pytest.fixture(autouse=True)
    async def setup_integration(self):
        """Przygotuj środowisko testowe"""
        # Inicjalizuj komponenty
        await data_flow_orchestrator.initialize()
        await integration_bridge.initialize()
        
        yield
        
        # Cleanup
        await data_flow_orchestrator.shutdown()
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_functionality(self):
        """Test funkcjonalności Circuit Breaker"""
        logger.info("🔌 Testing Circuit Breaker functionality...")
        
        # Konfiguracja Circuit Breaker z niskim progiem dla testów
        config = CircuitBreakerConfig(
            failure_threshold=2,
            recovery_timeout=5.0,
            success_threshold=1,
            timeout=1.0
        )
        
        circuit_breaker = resilience_manager.get_circuit_breaker("test_service", config)
        
        # Test 1: Circuit Breaker powinien być zamknięty na początku
        assert circuit_breaker.state.state.value == "closed"
        
        # Test 2: Symuluj błędy aby otworzyć Circuit Breaker
        async def failing_function():
            raise Exception("Simulated failure")
        
        # Wywołaj funkcję która zawsze się nie powiedzie
        for i in range(3):
            try:
                await circuit_breaker.call(failing_function)
            except:
                pass
        
        # Circuit Breaker powinien być teraz otwarty
        assert circuit_breaker.state.state.value == "open"
        
        # Test 3: Sprawdź czy Circuit Breaker blokuje wywołania
        with pytest.raises(Exception):
            await circuit_breaker.call(failing_function)
        
        logger.info("✅ Circuit Breaker functionality test passed")
    
    @pytest.mark.asyncio
    async def test_retry_mechanism(self):
        """Test mechanizmu retry"""
        logger.info("🔄 Testing Retry mechanism...")
        
        retry_config = RetryConfig(
            max_attempts=3,
            base_delay=0.1,
            max_delay=1.0,
            backoff_factor=2.0
        )
        
        retry_manager = resilience_manager.get_retry_manager("test_retry", retry_config)
        
        # Test 1: Funkcja która się powiedzie za drugim razem
        call_count = 0
        
        async def sometimes_failing_function():
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise Exception("Temporary failure")
            return "success"
        
        result = await retry_manager.execute_with_retry(sometimes_failing_function)
        assert result == "success"
        assert call_count == 2
        
        logger.info("✅ Retry mechanism test passed")
    
    @pytest.mark.asyncio
    async def test_data_flow_orchestrator(self):
        """Test orkiestratora przepływu danych"""
        logger.info("🌊 Testing Data Flow Orchestrator...")
        
        # Test 1: Przetwarzanie wiadomości transkrypcji
        transcription_message = DataFlowMessage(
            id="test_transcription_001",
            flow_type=DataFlowType.TRANSCRIPTION,
            priority=DataPriority.HIGH,
            source="test_source",
            destination="gobackend_crm",
            data={
                "email_id": "email_123",
                "file_path": "/path/to/audio.m4a",
                "transcript": "Test transcription content",
                "confidence": 0.95,
                "language": "pl"
            },
            metadata={
                "test": True,
                "processing_timestamp": datetime.now().isoformat()
            },
            timestamp=datetime.now()
        )
        
        # Przetwórz wiadomość
        success = await data_flow_orchestrator.process_message(transcription_message)
        assert success, "Message processing should succeed"
        
        # Test 2: Sprawdź metryki
        metrics = data_flow_orchestrator.get_metrics()
        assert metrics["messages_processed"] > 0
        assert metrics["validation_errors"] == 0
        
        logger.info("✅ Data Flow Orchestrator test passed")
    
    @pytest.mark.asyncio
    async def test_integration_bridge_health_monitoring(self):
        """Test monitorowania zdrowia mostu integracji"""
        logger.info("🏥 Testing Integration Bridge health monitoring...")
        
        # Sprawdź status zdrowia
        health_status = integration_bridge.get_integration_health()
        
        assert "health_score" in health_status
        assert "online_services" in health_status
        assert "total_services" in health_status
        assert "services" in health_status
        
        # Health score powinien być między 0 a 100
        assert 0 <= health_status["health_score"] <= 100
        
        logger.info("✅ Integration Bridge health monitoring test passed")
    
    @pytest.mark.asyncio
    async def test_end_to_end_transcription_flow(self):
        """Test end-to-end przepływu transkrypcji"""
        logger.info("🎯 Testing end-to-end transcription flow...")
        
        # Symuluj pełny przepływ transkrypcji
        email_id = f"test_email_{int(time.time())}"
        file_path = "/test/audio.m4a"
        
        # 1. Utwórz wiadomość transkrypcji
        message = DataFlowMessage(
            id=f"transcription_{email_id}",
            flow_type=DataFlowType.TRANSCRIPTION,
            priority=DataPriority.HIGH,
            source="python_mixer_transcription",
            destination="gobackend_crm",
            data={
                "email_id": email_id,
                "file_path": file_path,
                "transcript": "Dzień dobry, mam problem z klimatyzacją LG.",
                "confidence": 0.92,
                "language": "pl",
                "processing_time": 15.5
            },
            metadata={
                "source_service": "nvidia_stt",
                "model_version": "fastconformer_pl",
                "test_mode": True
            },
            timestamp=datetime.now()
        )
        
        # 2. Przetwórz przez orkiestrator
        success = await data_flow_orchestrator.process_message(message)
        assert success, "Transcription message processing should succeed"
        
        # 3. Sprawdź czy wiadomość została dodana do kolejki
        metrics = data_flow_orchestrator.get_metrics()
        assert metrics["messages_processed"] > 0
        
        # 4. Symuluj odpowiedź z GoBackend
        # W rzeczywistym scenariuszu GoBackend odpowiedziałby potwierdzeniem
        
        logger.info("✅ End-to-end transcription flow test passed")
    
    @pytest.mark.asyncio
    async def test_error_handling_and_recovery(self):
        """Test obsługi błędów i odzyskiwania"""
        logger.info("🛡️ Testing error handling and recovery...")
        
        # Test 1: Obsługa błędów walidacji
        invalid_message = DataFlowMessage(
            id="invalid_message",
            flow_type=DataFlowType.TRANSCRIPTION,
            priority=DataPriority.NORMAL,
            source="test",
            destination="gobackend",
            data={
                # Brakuje wymaganych pól
                "invalid_field": "value"
            },
            metadata={},
            timestamp=datetime.now()
        )
        
        success = await data_flow_orchestrator.process_message(invalid_message)
        assert not success, "Invalid message should fail validation"
        
        # Test 2: Sprawdź metryki błędów
        metrics = data_flow_orchestrator.get_metrics()
        assert metrics["validation_errors"] > 0
        
        logger.info("✅ Error handling and recovery test passed")
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self):
        """Test wydajności pod obciążeniem"""
        logger.info("⚡ Testing performance under load...")
        
        # Przygotuj wiele wiadomości
        messages = []
        for i in range(50):
            message = DataFlowMessage(
                id=f"load_test_{i}",
                flow_type=DataFlowType.EMAIL_PROCESSING,
                priority=DataPriority.NORMAL,
                source="load_test",
                destination="gobackend",
                data={
                    "email_id": f"email_{i}",
                    "subject": f"Test email {i}",
                    "sender": f"test{i}@example.com",
                    "body": f"Test email content {i}"
                },
                metadata={"load_test": True},
                timestamp=datetime.now()
            )
            messages.append(message)
        
        # Zmierz czas przetwarzania
        start_time = time.time()
        
        # Przetwórz wszystkie wiadomości równolegle
        tasks = [
            data_flow_orchestrator.process_message(msg) 
            for msg in messages
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Sprawdź wyniki
        successful_count = sum(1 for result in results if result is True)
        
        logger.info(f"📊 Processed {successful_count}/{len(messages)} messages in {processing_time:.2f}s")
        
        # Sprawdź czy większość wiadomości została przetworzona pomyślnie
        success_rate = successful_count / len(messages)
        assert success_rate > 0.8, f"Success rate {success_rate:.2%} is too low"
        
        # Sprawdź czy czas przetwarzania jest rozsądny (< 10s dla 50 wiadomości)
        assert processing_time < 10.0, f"Processing time {processing_time:.2f}s is too high"
        
        logger.info("✅ Performance under load test passed")
    
    @pytest.mark.asyncio
    async def test_resilience_integration(self):
        """Test integracji wszystkich mechanizmów odporności"""
        logger.info("🛡️ Testing resilience integration...")
        
        # Test kombinacji Circuit Breaker + Retry
        async def unreliable_service():
            import random
            if random.random() < 0.7:  # 70% szans na błąd
                raise Exception("Service temporarily unavailable")
            return "success"
        
        # Wykonaj z pełną odpornością
        try:
            result = await resilience_manager.execute_resilient(
                unreliable_service,
                "unreliable_test_service",
                CircuitBreakerConfig(failure_threshold=3, recovery_timeout=5.0),
                RetryConfig(max_attempts=5, base_delay=0.1)
            )
            logger.info(f"Resilient execution result: {result}")
        except Exception as e:
            logger.info(f"Resilient execution failed after all attempts: {e}")
        
        # Sprawdź statystyki
        stats = resilience_manager.get_all_stats()
        assert "metrics" in stats
        assert "circuit_breakers" in stats
        
        logger.info("✅ Resilience integration test passed")

@pytest.mark.asyncio
async def test_integration_startup_shutdown():
    """Test uruchamiania i zamykania integracji"""
    logger.info("🔄 Testing integration startup and shutdown...")
    
    # Test inicjalizacji
    await data_flow_orchestrator.initialize()
    assert data_flow_orchestrator.is_running
    
    # Test zamykania
    await data_flow_orchestrator.shutdown()
    assert not data_flow_orchestrator.is_running
    
    logger.info("✅ Integration startup/shutdown test passed")

if __name__ == "__main__":
    # Uruchom testy
    asyncio.run(test_integration_startup_shutdown())
    logger.info("🎉 All integration tests completed!")
