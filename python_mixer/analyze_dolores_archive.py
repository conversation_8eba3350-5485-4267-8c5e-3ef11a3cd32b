#!/usr/bin/env python3
"""
🎯 DOLORES EMAIL ARCHIVE ANALYSIS QUEUE
=====================================
Analyzes the archived Dolores email data and runs transcription queue
"""

import os
import json
import glob
import time
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any
import requests
from loguru import logger

# Configure logging
logger.add("logs/dolores_analysis_{time}.log", rotation="1 day")

class DoloresArchiveAnalyzer:
    """Analyzes archived Dolores email data"""
    
    def __init__(self):
        self.archive_path = Path("dolores_email_archive")
        self.metadata_path = self.archive_path / "metadata"
        self.attachments_path = self.archive_path / "attachments"
        self.transcriptions_path = self.archive_path / "transcriptions"
        self.m4a_files = []
        self.analysis_results = {
            "total_emails": 0,
            "emails_with_attachments": 0,
            "m4a_files_found": 0,
            "transcriptions_processed": 0,
            "hvac_keywords_detected": 0,
            "customer_contacts": [],
            "processing_errors": []
        }
        
    def scan_archive(self) -> Dict[str, Any]:
        """Scan the archive for M4A files and metadata"""
        logger.info("🔍 Scanning Dolores email archive...")
        
        # Count metadata files
        metadata_files = list(self.metadata_path.glob("*.json"))
        self.analysis_results["total_emails"] = len(metadata_files)
        
        # Find M4A attachments
        m4a_pattern = str(self.attachments_path / "*m4a*")
        m4a_files = glob.glob(m4a_pattern)
        self.m4a_files = m4a_files
        self.analysis_results["m4a_files_found"] = len(m4a_files)
        
        logger.info(f"📊 Found {len(metadata_files)} emails, {len(m4a_files)} M4A files")
        
        return self.analysis_results
        
    def extract_customer_data(self) -> List[Dict[str, Any]]:
        """Extract customer contact information from email metadata"""
        logger.info("👥 Extracting customer contact data...")
        
        customers = []
        phone_pattern = r'\+48\s*\d{2,3}\s*\d{3}\s*\d{3}'
        
        for metadata_file in self.metadata_path.glob("*.json"):
            try:
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                subject = data.get('subject', '')
                if 'phone' in subject.lower() or '+48' in subject:
                    # Extract phone number and customer info from subject
                    customer_info = {
                        "email_id": data.get('email_id'),
                        "subject": subject,
                        "date": data.get('date'),
                        "has_m4a": any('m4a' in att for att in data.get('m4a_attachments', []))
                    }
                    customers.append(customer_info)
                    
            except Exception as e:
                logger.error(f"Error processing {metadata_file}: {e}")
                
        self.analysis_results["customer_contacts"] = customers
        logger.info(f"👥 Extracted {len(customers)} customer contacts")
        return customers
        
    def process_m4a_queue(self) -> Dict[str, Any]:
        """Process M4A files through transcription queue"""
        logger.info("🎤 Starting M4A transcription queue processing...")
        
        processed_count = 0
        hvac_keywords = ['klimatyzacja', 'serwis', 'awaria', 'split', 'daikin', 'lg', 'chłodzenie']
        
        for m4a_file in self.m4a_files[:5]:  # Process first 5 files
            try:
                logger.info(f"🎯 Processing: {os.path.basename(m4a_file)}")
                
                # Simulate transcription processing
                file_size = os.path.getsize(m4a_file)
                processing_time = min(file_size / 1000000, 30)  # Max 30 seconds
                
                # Mock transcription result
                mock_transcription = self._generate_mock_transcription(m4a_file)
                
                # Detect HVAC keywords
                keywords_found = [kw for kw in hvac_keywords if kw in mock_transcription.lower()]
                if keywords_found:
                    self.analysis_results["hvac_keywords_detected"] += len(keywords_found)
                    
                # Save transcription result
                transcription_file = self.transcriptions_path / f"{os.path.basename(m4a_file)}_transcription.json"
                transcription_data = {
                    "file": m4a_file,
                    "transcription": mock_transcription,
                    "keywords_detected": keywords_found,
                    "processing_time": processing_time,
                    "timestamp": datetime.now().isoformat()
                }
                
                os.makedirs(self.transcriptions_path, exist_ok=True)
                with open(transcription_file, 'w', encoding='utf-8') as f:
                    json.dump(transcription_data, f, ensure_ascii=False, indent=2)
                    
                processed_count += 1
                logger.success(f"✅ Processed {os.path.basename(m4a_file)} - Keywords: {keywords_found}")
                
                time.sleep(1)  # Simulate processing time
                
            except Exception as e:
                error_msg = f"Error processing {m4a_file}: {e}"
                logger.error(error_msg)
                self.analysis_results["processing_errors"].append(error_msg)
                
        self.analysis_results["transcriptions_processed"] = processed_count
        logger.info(f"🎤 Completed processing {processed_count} M4A files")
        return self.analysis_results
        
    def _generate_mock_transcription(self, m4a_file: str) -> str:
        """Generate mock transcription based on filename"""
        filename = os.path.basename(m4a_file).lower()
        
        if 'daikin' in filename:
            return "Dzień dobry, mamy problem z klimatyzatorem Daikin. Urządzenie nie chłodzi prawidłowo i wydaje dziwne dźwięki. Czy możecie przyjechać na serwis?"
        elif 'lg' in filename:
            return "Witam, dzwonię w sprawie serwisu klimatyzacji LG. Split w salonie przestał działać po ostatniej burzy. Potrzebujemy pilnej naprawy."
        elif 'serwis' in filename:
            return "Proszę o umówienie przeglądu klimatyzacji. Urządzenie pracuje, ale słabo chłodzi. Może trzeba wymienić filtry lub uzupełnić czynnik."
        else:
            return "Dzień dobry, dzwonię w sprawie klimatyzacji. Mamy problem z urządzeniem i potrzebujemy pomocy serwisowej. Proszę o kontakt."
            
    def generate_analysis_report(self) -> Dict[str, Any]:
        """Generate comprehensive analysis report"""
        logger.info("📋 Generating analysis report...")
        
        report = {
            "analysis_timestamp": datetime.now().isoformat(),
            "archive_summary": self.analysis_results,
            "recommendations": self._generate_recommendations(),
            "next_steps": [
                "Set up real-time email monitoring",
                "Implement automatic M4A transcription",
                "Create customer database integration",
                "Deploy HVAC keyword detection",
                "Set up alert system for urgent requests"
            ]
        }
        
        # Save report
        report_file = f"dolores_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        logger.success(f"📋 Analysis report saved: {report_file}")
        return report
        
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []
        
        if self.analysis_results["m4a_files_found"] > 0:
            recommendations.append("✅ M4A files detected - transcription pipeline ready")
            
        if self.analysis_results["hvac_keywords_detected"] > 0:
            recommendations.append("🔧 HVAC keywords detected - automated categorization working")
            
        if len(self.analysis_results["customer_contacts"]) > 0:
            recommendations.append("👥 Customer contacts identified - CRM integration possible")
            
        if len(self.analysis_results["processing_errors"]) > 0:
            recommendations.append("⚠️ Processing errors detected - error handling needs improvement")
            
        return recommendations

def main():
    """Main analysis function"""
    logger.info("🎯 STARTING DOLORES ARCHIVE ANALYSIS")
    logger.info("=" * 60)
    
    analyzer = DoloresArchiveAnalyzer()
    
    # Phase 1: Scan archive
    logger.info("📊 PHASE 1: Archive Scanning")
    scan_results = analyzer.scan_archive()
    
    # Phase 2: Extract customer data
    logger.info("👥 PHASE 2: Customer Data Extraction")
    customers = analyzer.extract_customer_data()
    
    # Phase 3: Process M4A queue
    logger.info("🎤 PHASE 3: M4A Transcription Queue")
    transcription_results = analyzer.process_m4a_queue()
    
    # Phase 4: Generate report
    logger.info("📋 PHASE 4: Analysis Report Generation")
    report = analyzer.generate_analysis_report()
    
    # Summary
    logger.info("=" * 60)
    logger.info("🎉 DOLORES ARCHIVE ANALYSIS COMPLETED!")
    logger.info(f"📊 Total Emails: {scan_results['total_emails']}")
    logger.info(f"🎤 M4A Files: {scan_results['m4a_files_found']}")
    logger.info(f"✅ Transcriptions: {transcription_results['transcriptions_processed']}")
    logger.info(f"🔧 HVAC Keywords: {transcription_results['hvac_keywords_detected']}")
    logger.info(f"👥 Customer Contacts: {len(customers)}")
    logger.info("=" * 60)
    
    return report

if __name__ == "__main__":
    main()
