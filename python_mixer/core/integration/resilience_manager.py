#!/usr/bin/env python3
"""
🛡️ RESILIENCE MANAGER
Zarządza odpornością i niezawodnością integracji Python Mixer ↔ GoBackend-Kratos
Implementuje wzorce Circuit Breaker, Retry, Timeout i Bulkhead
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import aiohttp
from functools import wraps
import json
from pathlib import Path

logger = logging.getLogger(__name__)

class CircuitState(Enum):
    """Stan Circuit Breaker"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class RetryStrategy(Enum):
    """Strategia ponawiania"""
    FIXED_DELAY = "fixed_delay"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"

@dataclass
class CircuitBreakerConfig:
    """Konfiguracja Circuit Breaker"""
    failure_threshold: int = 5
    recovery_timeout: float = 30.0
    success_threshold: int = 3
    timeout: float = 10.0
    
@dataclass
class RetryConfig:
    """Konfiguracja Retry"""
    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    base_delay: float = 1.0
    max_delay: float = 60.0
    backoff_factor: float = 2.0
    jitter: bool = True

@dataclass
class CircuitBreakerState:
    """Stan Circuit Breaker"""
    state: CircuitState = CircuitState.CLOSED
    failure_count: int = 0
    success_count: int = 0
    last_failure_time: Optional[datetime] = None
    last_success_time: Optional[datetime] = None
    total_requests: int = 0
    total_failures: int = 0
    total_successes: int = 0

class CircuitBreaker:
    """🔌 Circuit Breaker Implementation"""
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitBreakerState()
        self._lock = asyncio.Lock()
    
    async def call(self, func: Callable, *args, **kwargs):
        """Wykonaj funkcję przez Circuit Breaker"""
        async with self._lock:
            if self.state.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state.state = CircuitState.HALF_OPEN
                    logger.info(f"🔄 Circuit breaker {self.name} moved to HALF_OPEN")
                else:
                    raise CircuitBreakerOpenError(f"Circuit breaker {self.name} is OPEN")
        
        self.state.total_requests += 1
        start_time = time.time()
        
        try:
            # Wykonaj funkcję z timeout
            result = await asyncio.wait_for(
                func(*args, **kwargs),
                timeout=self.config.timeout
            )
            
            # Sukces
            await self._on_success()
            return result
            
        except asyncio.TimeoutError:
            await self._on_failure()
            raise CircuitBreakerTimeoutError(f"Timeout in circuit breaker {self.name}")
        except Exception as e:
            await self._on_failure()
            raise
    
    async def _on_success(self):
        """Obsłuż sukces"""
        async with self._lock:
            self.state.total_successes += 1
            self.state.last_success_time = datetime.now()
            
            if self.state.state == CircuitState.HALF_OPEN:
                self.state.success_count += 1
                if self.state.success_count >= self.config.success_threshold:
                    self.state.state = CircuitState.CLOSED
                    self.state.failure_count = 0
                    self.state.success_count = 0
                    logger.info(f"✅ Circuit breaker {self.name} moved to CLOSED")
            elif self.state.state == CircuitState.CLOSED:
                self.state.failure_count = 0
    
    async def _on_failure(self):
        """Obsłuż błąd"""
        async with self._lock:
            self.state.total_failures += 1
            self.state.failure_count += 1
            self.state.last_failure_time = datetime.now()
            
            if (self.state.state == CircuitState.CLOSED and 
                self.state.failure_count >= self.config.failure_threshold):
                self.state.state = CircuitState.OPEN
                logger.warning(f"⚠️ Circuit breaker {self.name} moved to OPEN")
            elif self.state.state == CircuitState.HALF_OPEN:
                self.state.state = CircuitState.OPEN
                self.state.success_count = 0
                logger.warning(f"⚠️ Circuit breaker {self.name} moved back to OPEN")
    
    def _should_attempt_reset(self) -> bool:
        """Sprawdź czy można próbować reset"""
        if self.state.last_failure_time is None:
            return True
        
        time_since_failure = datetime.now() - self.state.last_failure_time
        return time_since_failure.total_seconds() >= self.config.recovery_timeout
    
    def get_stats(self) -> Dict[str, Any]:
        """Pobierz statystyki"""
        return {
            "name": self.name,
            "state": self.state.state.value,
            "failure_count": self.state.failure_count,
            "success_count": self.state.success_count,
            "total_requests": self.state.total_requests,
            "total_failures": self.state.total_failures,
            "total_successes": self.state.total_successes,
            "success_rate": (
                self.state.total_successes / self.state.total_requests 
                if self.state.total_requests > 0 else 0
            ),
            "last_failure_time": (
                self.state.last_failure_time.isoformat() 
                if self.state.last_failure_time else None
            ),
            "last_success_time": (
                self.state.last_success_time.isoformat() 
                if self.state.last_success_time else None
            )
        }

class RetryManager:
    """🔄 Retry Manager"""
    
    def __init__(self, config: RetryConfig):
        self.config = config
    
    async def execute_with_retry(self, func: Callable, *args, **kwargs):
        """Wykonaj funkcję z retry"""
        last_exception = None
        
        for attempt in range(self.config.max_attempts):
            try:
                result = await func(*args, **kwargs)
                if attempt > 0:
                    logger.info(f"✅ Retry succeeded on attempt {attempt + 1}")
                return result
                
            except Exception as e:
                last_exception = e
                
                if attempt < self.config.max_attempts - 1:
                    delay = self._calculate_delay(attempt)
                    logger.warning(f"⚠️ Attempt {attempt + 1} failed, retrying in {delay:.2f}s: {e}")
                    await asyncio.sleep(delay)
                else:
                    logger.error(f"❌ All {self.config.max_attempts} attempts failed")
        
        raise last_exception
    
    def _calculate_delay(self, attempt: int) -> float:
        """Oblicz opóźnienie dla retry"""
        if self.config.strategy == RetryStrategy.FIXED_DELAY:
            delay = self.config.base_delay
        elif self.config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = self.config.base_delay * (self.config.backoff_factor ** attempt)
        elif self.config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = self.config.base_delay * (attempt + 1)
        else:
            delay = self.config.base_delay
        
        # Ogranicz maksymalne opóźnienie
        delay = min(delay, self.config.max_delay)
        
        # Dodaj jitter jeśli włączony
        if self.config.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)
        
        return delay

class ResilienceManager:
    """🛡️ Główny manager odporności"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_managers: Dict[str, RetryManager] = {}
        self.default_circuit_config = CircuitBreakerConfig()
        self.default_retry_config = RetryConfig()
        
        # Metryki
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "circuit_breaker_trips": 0,
            "retry_attempts": 0,
            "timeout_errors": 0
        }
    
    def get_circuit_breaker(self, name: str, config: Optional[CircuitBreakerConfig] = None) -> CircuitBreaker:
        """Pobierz lub utwórz Circuit Breaker"""
        if name not in self.circuit_breakers:
            cb_config = config or self.default_circuit_config
            self.circuit_breakers[name] = CircuitBreaker(name, cb_config)
        
        return self.circuit_breakers[name]
    
    def get_retry_manager(self, name: str, config: Optional[RetryConfig] = None) -> RetryManager:
        """Pobierz lub utwórz Retry Manager"""
        if name not in self.retry_managers:
            retry_config = config or self.default_retry_config
            self.retry_managers[name] = RetryManager(retry_config)
        
        return self.retry_managers[name]
    
    async def execute_resilient(
        self, 
        func: Callable, 
        service_name: str,
        circuit_config: Optional[CircuitBreakerConfig] = None,
        retry_config: Optional[RetryConfig] = None,
        *args, 
        **kwargs
    ):
        """Wykonaj funkcję z pełną odpornością (Circuit Breaker + Retry)"""
        circuit_breaker = self.get_circuit_breaker(service_name, circuit_config)
        retry_manager = self.get_retry_manager(service_name, retry_config)
        
        self.metrics["total_requests"] += 1
        
        try:
            # Wykonaj z retry i circuit breaker
            result = await retry_manager.execute_with_retry(
                circuit_breaker.call, func, *args, **kwargs
            )
            
            self.metrics["successful_requests"] += 1
            return result
            
        except CircuitBreakerOpenError:
            self.metrics["circuit_breaker_trips"] += 1
            self.metrics["failed_requests"] += 1
            raise
        except CircuitBreakerTimeoutError:
            self.metrics["timeout_errors"] += 1
            self.metrics["failed_requests"] += 1
            raise
        except Exception:
            self.metrics["failed_requests"] += 1
            raise
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Pobierz wszystkie statystyki"""
        circuit_breaker_stats = {
            name: cb.get_stats() 
            for name, cb in self.circuit_breakers.items()
        }
        
        return {
            "metrics": self.metrics,
            "circuit_breakers": circuit_breaker_stats,
            "timestamp": datetime.now().isoformat()
        }

# Wyjątki
class CircuitBreakerOpenError(Exception):
    """Circuit Breaker jest otwarty"""
    pass

class CircuitBreakerTimeoutError(Exception):
    """Timeout w Circuit Breaker"""
    pass

# Globalna instancja
resilience_manager = ResilienceManager()
