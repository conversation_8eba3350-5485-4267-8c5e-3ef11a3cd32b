#!/usr/bin/env python3
"""
🌊 DATA FLOW ORCHESTRATOR
Zarządza przepływem danych między Python Mixer a GoBackend-Kratos
Zapewnia optymalizację, walidację i transformację danych
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum
import json
from pathlib import Path
import aiohttp
from concurrent.futures import ThreadPoolExecutor

from .resilience_manager import resilience_manager, CircuitBreakerConfig, RetryConfig
from .event_system import RealTimeEventSystem, EventType, EventPriority

logger = logging.getLogger(__name__)

class DataFlowType(Enum):
    """Typ przepływu danych"""
    EMAIL_PROCESSING = "email_processing"
    TRANSCRIPTION = "transcription"
    CUSTOMER_SYNC = "customer_sync"
    EQUIPMENT_UPDATE = "equipment_update"
    TELEGRAM_MESSAGE = "telegram_message"
    HEALTH_STATUS = "health_status"

class DataPriority(Enum):
    """Priorytet danych"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class DataFlowMessage:
    """Wiadomość w przepływie danych"""
    id: str
    flow_type: DataFlowType
    priority: DataPriority
    source: str
    destination: str
    data: Dict[str, Any]
    metadata: Dict[str, Any]
    timestamp: datetime
    retry_count: int = 0
    max_retries: int = 3
    processing_time: Optional[float] = None
    validation_errors: List[str] = None

@dataclass
class DataTransformation:
    """Transformacja danych"""
    name: str
    input_schema: Dict[str, Any]
    output_schema: Dict[str, Any]
    transform_func: Callable
    validation_func: Optional[Callable] = None

class DataValidator:
    """🔍 Walidator danych"""
    
    def __init__(self):
        self.schemas = {}
        self.validation_rules = {}
    
    def register_schema(self, flow_type: DataFlowType, schema: Dict[str, Any]):
        """Zarejestruj schemat dla typu przepływu"""
        self.schemas[flow_type] = schema
    
    def validate_message(self, message: DataFlowMessage) -> List[str]:
        """Waliduj wiadomość"""
        errors = []
        
        # Podstawowa walidacja
        if not message.id:
            errors.append("Message ID is required")
        
        if not message.data:
            errors.append("Message data is required")
        
        # Walidacja schematu
        if message.flow_type in self.schemas:
            schema_errors = self._validate_against_schema(
                message.data, 
                self.schemas[message.flow_type]
            )
            errors.extend(schema_errors)
        
        return errors
    
    def _validate_against_schema(self, data: Dict[str, Any], schema: Dict[str, Any]) -> List[str]:
        """Waliduj dane względem schematu"""
        errors = []
        
        # Sprawdź wymagane pola
        required_fields = schema.get("required", [])
        for field in required_fields:
            if field not in data:
                errors.append(f"Required field '{field}' is missing")
        
        # Sprawdź typy danych
        field_types = schema.get("properties", {})
        for field, expected_type in field_types.items():
            if field in data:
                if not self._check_type(data[field], expected_type):
                    errors.append(f"Field '{field}' has invalid type")
        
        return errors
    
    def _check_type(self, value: Any, expected_type: str) -> bool:
        """Sprawdź typ danych"""
        type_mapping = {
            "string": str,
            "integer": int,
            "number": (int, float),
            "boolean": bool,
            "array": list,
            "object": dict
        }
        
        expected_python_type = type_mapping.get(expected_type)
        if expected_python_type:
            return isinstance(value, expected_python_type)
        
        return True

class DataTransformer:
    """🔄 Transformer danych"""
    
    def __init__(self):
        self.transformations = {}
    
    def register_transformation(self, flow_type: DataFlowType, transformation: DataTransformation):
        """Zarejestruj transformację"""
        self.transformations[flow_type] = transformation
    
    async def transform_message(self, message: DataFlowMessage) -> DataFlowMessage:
        """Transformuj wiadomość"""
        if message.flow_type not in self.transformations:
            return message
        
        transformation = self.transformations[message.flow_type]
        
        try:
            # Walidacja wejściowa
            if transformation.validation_func:
                validation_errors = transformation.validation_func(message.data)
                if validation_errors:
                    message.validation_errors = validation_errors
                    return message
            
            # Transformacja
            transformed_data = await self._execute_transformation(
                transformation.transform_func, 
                message.data
            )
            
            # Utwórz nową wiadomość z transformowanymi danymi
            transformed_message = DataFlowMessage(
                id=message.id,
                flow_type=message.flow_type,
                priority=message.priority,
                source=message.source,
                destination=message.destination,
                data=transformed_data,
                metadata=message.metadata,
                timestamp=message.timestamp,
                retry_count=message.retry_count,
                max_retries=message.max_retries
            )
            
            return transformed_message
            
        except Exception as e:
            logger.error(f"❌ Transformation failed for {message.flow_type}: {e}")
            message.validation_errors = [f"Transformation error: {str(e)}"]
            return message
    
    async def _execute_transformation(self, transform_func: Callable, data: Dict[str, Any]) -> Dict[str, Any]:
        """Wykonaj transformację"""
        if asyncio.iscoroutinefunction(transform_func):
            return await transform_func(data)
        else:
            # Wykonaj w thread pool dla funkcji synchronicznych
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                return await loop.run_in_executor(executor, transform_func, data)

class DataFlowOrchestrator:
    """🌊 Główny orkiestrator przepływu danych"""
    
    def __init__(self):
        self.validator = DataValidator()
        self.transformer = DataTransformer()
        self.event_system = RealTimeEventSystem()
        
        # Kolejki priorytetowe
        self.priority_queues = {
            DataPriority.CRITICAL: asyncio.Queue(),
            DataPriority.HIGH: asyncio.Queue(),
            DataPriority.NORMAL: asyncio.Queue(),
            DataPriority.LOW: asyncio.Queue()
        }
        
        # Konfiguracja resilience
        self.circuit_config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=30.0,
            success_threshold=2,
            timeout=15.0
        )
        
        self.retry_config = RetryConfig(
            max_attempts=3,
            base_delay=1.0,
            max_delay=30.0,
            backoff_factor=2.0
        )
        
        # Metryki
        self.metrics = {
            "messages_processed": 0,
            "messages_failed": 0,
            "validation_errors": 0,
            "transformation_errors": 0,
            "total_processing_time": 0.0,
            "average_processing_time": 0.0
        }
        
        # Status
        self.is_running = False
        self.workers = []
    
    async def initialize(self):
        """Inicjalizuj orkiestrator"""
        logger.info("🌊 Initializing Data Flow Orchestrator...")
        
        # Inicjalizuj event system
        await self.event_system.initialize()
        
        # Zarejestruj schematy walidacji
        self._register_validation_schemas()
        
        # Zarejestruj transformacje
        self._register_transformations()
        
        # Uruchom workery
        await self._start_workers()
        
        self.is_running = True
        logger.info("✅ Data Flow Orchestrator initialized successfully")
    
    async def shutdown(self):
        """Zamknij orkiestrator"""
        logger.info("🔄 Shutting down Data Flow Orchestrator...")
        
        self.is_running = False
        
        # Zatrzymaj workery
        for worker in self.workers:
            worker.cancel()
        
        await asyncio.gather(*self.workers, return_exceptions=True)
        
        logger.info("✅ Data Flow Orchestrator shut down successfully")
    
    async def process_message(self, message: DataFlowMessage) -> bool:
        """Przetwórz wiadomość"""
        start_time = datetime.now()
        
        try:
            # Walidacja
            validation_errors = self.validator.validate_message(message)
            if validation_errors:
                message.validation_errors = validation_errors
                self.metrics["validation_errors"] += 1
                logger.warning(f"⚠️ Validation errors for message {message.id}: {validation_errors}")
                return False
            
            # Transformacja
            transformed_message = await self.transformer.transform_message(message)
            if transformed_message.validation_errors:
                self.metrics["transformation_errors"] += 1
                logger.error(f"❌ Transformation errors for message {message.id}: {transformed_message.validation_errors}")
                return False
            
            # Dodaj do odpowiedniej kolejki priorytetowej
            await self.priority_queues[message.priority].put(transformed_message)
            
            # Aktualizuj metryki
            processing_time = (datetime.now() - start_time).total_seconds()
            self.metrics["messages_processed"] += 1
            self.metrics["total_processing_time"] += processing_time
            self.metrics["average_processing_time"] = (
                self.metrics["total_processing_time"] / self.metrics["messages_processed"]
            )
            
            # Wyemituj zdarzenie
            await self.event_system.emit(
                event_type=EventType.DATA_PROCESSED,
                source="data_flow_orchestrator",
                data={
                    "message_id": message.id,
                    "flow_type": message.flow_type.value,
                    "processing_time": processing_time
                }
            )
            
            return True
            
        except Exception as e:
            self.metrics["messages_failed"] += 1
            logger.error(f"❌ Failed to process message {message.id}: {e}")
            
            await self.event_system.emit(
                event_type=EventType.DATA_PROCESSING_FAILED,
                source="data_flow_orchestrator",
                data={
                    "message_id": message.id,
                    "error": str(e)
                },
                priority=EventPriority.HIGH
            )
            
            return False
    
    async def _start_workers(self):
        """Uruchom workery do przetwarzania kolejek"""
        # Worker dla każdego priorytetu
        for priority in DataPriority:
            worker = asyncio.create_task(self._priority_worker(priority))
            self.workers.append(worker)
        
        logger.info(f"🔄 Started {len(self.workers)} data flow workers")
    
    async def _priority_worker(self, priority: DataPriority):
        """Worker dla konkretnego priorytetu"""
        queue = self.priority_queues[priority]
        
        while self.is_running:
            try:
                # Pobierz wiadomość z kolejki
                message = await queue.get()
                
                # Przetwórz z resilience
                await self._process_with_resilience(message)
                
            except Exception as e:
                logger.error(f"❌ Error in {priority.name} priority worker: {e}")
                await asyncio.sleep(1)
    
    async def _process_with_resilience(self, message: DataFlowMessage):
        """Przetwórz wiadomość z resilience patterns"""
        service_name = f"data_flow_{message.flow_type.value}"
        
        try:
            await resilience_manager.execute_resilient(
                self._send_to_gobackend,
                service_name,
                self.circuit_config,
                self.retry_config,
                message
            )
            
        except Exception as e:
            logger.error(f"❌ Failed to send message {message.id} to GoBackend: {e}")
            
            # Retry logic
            if message.retry_count < message.max_retries:
                message.retry_count += 1
                await asyncio.sleep(2 ** message.retry_count)  # Exponential backoff
                await self.priority_queues[message.priority].put(message)
    
    async def _send_to_gobackend(self, message: DataFlowMessage):
        """Wyślij wiadomość do GoBackend"""
        # Implementacja wysyłania do GoBackend-Kratos
        # To będzie używane przez resilience manager
        
        gobackend_url = "http://localhost:8080/api/integration/message"
        
        payload = {
            "id": message.id,
            "type": message.flow_type.value,
            "priority": message.priority.value,
            "source": message.source,
            "data": message.data,
            "metadata": message.metadata,
            "timestamp": message.timestamp.isoformat()
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(gobackend_url, json=payload) as response:
                if response.status != 200:
                    raise Exception(f"GoBackend returned status {response.status}")
                
                result = await response.json()
                logger.info(f"✅ Message {message.id} sent to GoBackend successfully")
                return result
    
    def _register_validation_schemas(self):
        """Zarejestruj schematy walidacji"""
        # Schemat dla transkrypcji
        transcription_schema = {
            "required": ["email_id", "file_path", "transcript"],
            "properties": {
                "email_id": "string",
                "file_path": "string",
                "transcript": "string",
                "confidence": "number",
                "language": "string"
            }
        }
        self.validator.register_schema(DataFlowType.TRANSCRIPTION, transcription_schema)
        
        # Schemat dla emaili
        email_schema = {
            "required": ["email_id", "subject", "sender"],
            "properties": {
                "email_id": "string",
                "subject": "string",
                "sender": "string",
                "recipient": "string",
                "body": "string"
            }
        }
        self.validator.register_schema(DataFlowType.EMAIL_PROCESSING, email_schema)
    
    def _register_transformations(self):
        """Zarejestruj transformacje danych"""
        # Transformacja dla transkrypcji
        def transform_transcription(data: Dict[str, Any]) -> Dict[str, Any]:
            # Dodaj metadane HVAC
            transformed = data.copy()
            transformed["hvac_metadata"] = {
                "processed_by": "python_mixer",
                "processing_timestamp": datetime.now().isoformat(),
                "language_detected": data.get("language", "pl"),
                "confidence_level": "high" if data.get("confidence", 0) > 0.8 else "medium"
            }
            return transformed
        
        transcription_transform = DataTransformation(
            name="transcription_transform",
            input_schema={},
            output_schema={},
            transform_func=transform_transcription
        )
        
        self.transformer.register_transformation(
            DataFlowType.TRANSCRIPTION, 
            transcription_transform
        )
    
    def get_metrics(self) -> Dict[str, Any]:
        """Pobierz metryki"""
        return {
            **self.metrics,
            "queue_sizes": {
                priority.name: queue.qsize() 
                for priority, queue in self.priority_queues.items()
            },
            "resilience_stats": resilience_manager.get_all_stats(),
            "timestamp": datetime.now().isoformat()
        }

# Globalna instancja
data_flow_orchestrator = DataFlowOrchestrator()
