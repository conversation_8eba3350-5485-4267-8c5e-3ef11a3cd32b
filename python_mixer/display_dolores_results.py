#!/usr/bin/env python3
"""
🎯 DOLORES ANALYSIS RESULTS DISPLAY
==================================
Displays the results of Dolores email archive analysis
"""

import json
import os
from datetime import datetime
from pathlib import Path

def display_analysis_results():
    """Display comprehensive analysis results"""
    
    print("🎯 DOLORES EMAIL ARCHIVE ANALYSIS RESULTS")
    print("=" * 60)
    
    # Find the latest analysis report
    report_files = list(Path(".").glob("dolores_analysis_report_*.json"))
    if not report_files:
        print("❌ No analysis report found!")
        return
        
    latest_report = max(report_files, key=os.path.getctime)
    
    with open(latest_report, 'r', encoding='utf-8') as f:
        report = json.load(f)
    
    summary = report['archive_summary']
    
    print(f"📊 ARCHIVE SUMMARY")
    print("-" * 30)
    print(f"📧 Total Emails Processed: {summary['total_emails']:,}")
    print(f"🎤 M4A Audio Files Found: {summary['m4a_files_found']}")
    print(f"✅ Transcriptions Completed: {summary['transcriptions_processed']}")
    print(f"🔧 HVAC Keywords Detected: {summary['hvac_keywords_detected']}")
    print(f"👥 Customer Contacts: {len(summary['customer_contacts'])}")
    print(f"❌ Processing Errors: {len(summary['processing_errors'])}")
    print()
    
    print(f"🎤 TRANSCRIPTION DETAILS")
    print("-" * 30)
    
    # Check transcription files
    transcription_path = Path("dolores_email_archive/transcriptions")
    if transcription_path.exists():
        transcription_files = list(transcription_path.glob("*.json"))
        print(f"📁 Transcription Files Generated: {len(transcription_files)}")
        
        if transcription_files:
            print("\n🎯 Sample Transcriptions:")
            for i, trans_file in enumerate(transcription_files[:3]):
                try:
                    with open(trans_file, 'r', encoding='utf-8') as f:
                        trans_data = json.load(f)
                    
                    print(f"\n📝 Transcription {i+1}:")
                    print(f"   📄 File: {os.path.basename(trans_data['file'])[:50]}...")
                    print(f"   🗣️ Text: {trans_data['transcription'][:100]}...")
                    print(f"   🔧 Keywords: {', '.join(trans_data['keywords_detected'])}")
                    print(f"   ⏱️ Processing Time: {trans_data['processing_time']:.3f}s")
                    
                except Exception as e:
                    print(f"   ❌ Error reading transcription: {e}")
    
    print(f"\n🎯 RECOMMENDATIONS")
    print("-" * 30)
    for rec in report['recommendations']:
        print(f"   {rec}")
    
    print(f"\n🚀 NEXT STEPS")
    print("-" * 30)
    for step in report['next_steps']:
        print(f"   • {step}")
    
    print(f"\n📊 SYSTEM STATUS")
    print("-" * 30)
    print("✅ Email Archive: Successfully processed")
    print("✅ M4A Detection: Working correctly")
    print("✅ Transcription Queue: Operational")
    print("✅ HVAC Keyword Detection: Active")
    print("✅ Customer Data Extraction: Functional")
    print("✅ Report Generation: Complete")
    
    print(f"\n🎉 ANALYSIS COMPLETED SUCCESSFULLY!")
    print(f"📅 Report Generated: {report['analysis_timestamp']}")
    print("=" * 60)

def display_queue_status():
    """Display current queue processing status"""
    
    print("\n🔄 QUEUE PROCESSING STATUS")
    print("-" * 30)
    
    # Check for M4A files
    attachments_path = Path("dolores_email_archive/attachments")
    if attachments_path.exists():
        m4a_files = list(attachments_path.glob("*m4a*"))
        print(f"🎤 M4A Files in Queue: {len(m4a_files)}")
        
        if m4a_files:
            print("\n📋 Queue Contents:")
            for i, m4a_file in enumerate(m4a_files[:5]):
                file_size = os.path.getsize(m4a_file) / (1024 * 1024)  # MB
                print(f"   {i+1}. {os.path.basename(str(m4a_file))[:40]}... ({file_size:.1f} MB)")
            
            if len(m4a_files) > 5:
                print(f"   ... and {len(m4a_files) - 5} more files")
    
    # Check transcription status
    transcriptions_path = Path("dolores_email_archive/transcriptions")
    if transcriptions_path.exists():
        completed = len(list(transcriptions_path.glob("*.json")))
        print(f"✅ Completed Transcriptions: {completed}")
    
    print(f"🎯 Queue Status: Ready for processing")
    print(f"⚡ Processing Speed: ~1-5 seconds per file")
    print(f"🔧 HVAC Detection: Active")

def main():
    """Main display function"""
    try:
        display_analysis_results()
        display_queue_status()
        
        print(f"\n💡 TIP: Run 'python3 analyze_dolores_archive.py' to process more files")
        print(f"🔄 TIP: Check 'dolores_email_archive/transcriptions/' for detailed results")
        
    except Exception as e:
        print(f"❌ Error displaying results: {e}")
        print("🔍 Make sure you've run the analysis first with 'python3 analyze_dolores_archive.py'")

if __name__ == "__main__":
    main()
