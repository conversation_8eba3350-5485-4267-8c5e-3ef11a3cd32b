package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/gorilla/websocket"
	"github.com/sony/gobreaker"
	"go.uber.org/zap"
)

// 🌉 Enhanced Integration Bridge - Production-Ready Python Mixer Integration
// Provides robust, resilient communication between GoBackend-Kratos and python_mixer

type EnhancedIntegrationBridge struct {
	// Core components
	logger     *log.Helper
	zapLogger  *zap.Logger
	httpClient *http.Client
	wsUpgrader websocket.Upgrader

	// Circuit breakers for different services
	circuitBreakers map[string]*gobreaker.CircuitBreaker

	// Connection management
	activeConnections map[string]*websocket.Conn
	connectionMutex   sync.RWMutex

	// Configuration
	config *BridgeConfig

	// Metrics
	metrics *BridgeMetrics

	// Health monitoring
	healthChecker *HealthChecker

	// Message queues
	incomingQueue chan *BridgeMessage
	outgoingQueue chan *BridgeMessage

	// Shutdown management
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

type BridgeConfig struct {
	// Python Mixer endpoints
	PythonMixerBaseURL  string `json:"python_mixer_base_url"`
	OrchestratorPort    int    `json:"orchestrator_port"`
	EmailProcessingPort int    `json:"email_processing_port"`
	TranscriptionPort   int    `json:"transcription_port"`
	TelegramBotPort     int    `json:"telegram_bot_port"`

	// Connection settings
	MaxConnections    int           `json:"max_connections"`
	ConnectionTimeout time.Duration `json:"connection_timeout"`
	ReadTimeout       time.Duration `json:"read_timeout"`
	WriteTimeout      time.Duration `json:"write_timeout"`

	// Circuit breaker settings
	CircuitBreakerSettings map[string]*CircuitBreakerConfig `json:"circuit_breaker_settings"`

	// Retry settings
	MaxRetries         int           `json:"max_retries"`
	RetryDelay         time.Duration `json:"retry_delay"`
	RetryBackoffFactor float64       `json:"retry_backoff_factor"`

	// Health check settings
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	HealthCheckTimeout  time.Duration `json:"health_check_timeout"`

	// Queue settings
	QueueSize         int `json:"queue_size"`
	ProcessingWorkers int `json:"processing_workers"`
}

type CircuitBreakerConfig struct {
	MaxRequests  uint32        `json:"max_requests"`
	Interval     time.Duration `json:"interval"`
	Timeout      time.Duration `json:"timeout"`
	FailureRatio float64       `json:"failure_ratio"`
	MinRequests  uint32        `json:"min_requests"`
}

type BridgeMetrics struct {
	// Connection metrics
	TotalConnections  int64 `json:"total_connections"`
	ActiveConnections int64 `json:"active_connections"`
	FailedConnections int64 `json:"failed_connections"`

	// Message metrics
	MessagesSent      int64 `json:"messages_sent"`
	MessagesReceived  int64 `json:"messages_received"`
	MessagesProcessed int64 `json:"messages_processed"`
	MessagesFailed    int64 `json:"messages_failed"`

	// Performance metrics
	AverageResponseTime float64 `json:"average_response_time"`
	TotalProcessingTime float64 `json:"total_processing_time"`

	// Error metrics
	CircuitBreakerTrips int64 `json:"circuit_breaker_trips"`
	RetryAttempts       int64 `json:"retry_attempts"`
	TimeoutErrors       int64 `json:"timeout_errors"`

	// Health metrics
	LastHealthCheck     time.Time `json:"last_health_check"`
	HealthCheckFailures int64     `json:"health_check_failures"`

	mutex sync.RWMutex
}

type BridgeMessage struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Source      string                 `json:"source"`
	Destination string                 `json:"destination"`
	Data        map[string]interface{} `json:"data"`
	Timestamp   time.Time              `json:"timestamp"`
	Priority    MessagePriority        `json:"priority"`
	RetryCount  int                    `json:"retry_count"`
	Metadata    map[string]string      `json:"metadata"`
}

type MessagePriority int

const (
	PriorityLow MessagePriority = iota
	PriorityNormal
	PriorityHigh
	PriorityCritical
)

type HealthChecker struct {
	bridge    *EnhancedIntegrationBridge
	endpoints map[string]string
	results   map[string]*HealthResult
	mutex     sync.RWMutex
}

type HealthResult struct {
	Endpoint     string        `json:"endpoint"`
	Status       string        `json:"status"`
	ResponseTime time.Duration `json:"response_time"`
	LastCheck    time.Time     `json:"last_check"`
	Error        string        `json:"error,omitempty"`
}

// NewEnhancedIntegrationBridge creates a new enhanced integration bridge
func NewEnhancedIntegrationBridge(config *BridgeConfig, logger log.Logger, zapLogger *zap.Logger) *EnhancedIntegrationBridge {
	ctx, cancel := context.WithCancel(context.Background())

	bridge := &EnhancedIntegrationBridge{
		logger:            log.NewHelper(logger),
		zapLogger:         zapLogger,
		config:            config,
		ctx:               ctx,
		cancel:            cancel,
		circuitBreakers:   make(map[string]*gobreaker.CircuitBreaker),
		activeConnections: make(map[string]*websocket.Conn),
		metrics:           &BridgeMetrics{},
		incomingQueue:     make(chan *BridgeMessage, config.QueueSize),
		outgoingQueue:     make(chan *BridgeMessage, config.QueueSize),
		wsUpgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // Configure properly for production
			},
		},
	}

	// Initialize HTTP client with timeouts
	bridge.httpClient = &http.Client{
		Timeout: config.ConnectionTimeout,
		Transport: &http.Transport{
			MaxIdleConns:        config.MaxConnections,
			MaxIdleConnsPerHost: config.MaxConnections / 4,
			IdleConnTimeout:     30 * time.Second,
		},
	}

	// Initialize circuit breakers
	bridge.initializeCircuitBreakers()

	// Initialize health checker
	bridge.healthChecker = &HealthChecker{
		bridge:    bridge,
		endpoints: bridge.buildHealthCheckEndpoints(),
		results:   make(map[string]*HealthResult),
	}

	return bridge
}

// Initialize starts the enhanced integration bridge
func (b *EnhancedIntegrationBridge) Initialize() error {
	b.logger.Info("🌉 Initializing Enhanced Integration Bridge...")

	// Start background workers
	for i := 0; i < b.config.ProcessingWorkers; i++ {
		b.wg.Add(1)
		go b.messageProcessor(i)
	}

	// Start health monitoring
	b.wg.Add(1)
	go b.healthMonitor()

	// Start metrics collector
	b.wg.Add(1)
	go b.metricsCollector()

	// Perform initial health check
	if err := b.performInitialHealthCheck(); err != nil {
		b.logger.Errorf("Initial health check failed: %v", err)
		return fmt.Errorf("initial health check failed: %w", err)
	}

	b.logger.Info("✅ Enhanced Integration Bridge initialized successfully")
	return nil
}

// Shutdown gracefully shuts down the bridge
func (b *EnhancedIntegrationBridge) Shutdown() error {
	b.logger.Info("🔄 Shutting down Enhanced Integration Bridge...")

	// Cancel context to signal shutdown
	b.cancel()

	// Close all WebSocket connections
	b.connectionMutex.Lock()
	for id, conn := range b.activeConnections {
		conn.Close()
		delete(b.activeConnections, id)
	}
	b.connectionMutex.Unlock()

	// Wait for all goroutines to finish
	b.wg.Wait()

	b.logger.Info("✅ Enhanced Integration Bridge shut down successfully")
	return nil
}

// initializeCircuitBreakers sets up circuit breakers for different services
func (b *EnhancedIntegrationBridge) initializeCircuitBreakers() {
	services := []string{"orchestrator", "email_processing", "transcription", "telegram_bot"}

	for _, service := range services {
		config := b.config.CircuitBreakerSettings[service]
		if config == nil {
			// Default configuration
			config = &CircuitBreakerConfig{
				MaxRequests:  3,
				Interval:     60 * time.Second,
				Timeout:      30 * time.Second,
				FailureRatio: 0.6,
				MinRequests:  3,
			}
		}

		settings := gobreaker.Settings{
			Name:        fmt.Sprintf("python_mixer_%s", service),
			MaxRequests: config.MaxRequests,
			Interval:    config.Interval,
			Timeout:     config.Timeout,
			ReadyToTrip: func(counts gobreaker.Counts) bool {
				failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
				return counts.Requests >= config.MinRequests && failureRatio >= config.FailureRatio
			},
			OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
				b.zapLogger.Info("Circuit breaker state changed",
					zap.String("service", service),
					zap.String("from", from.String()),
					zap.String("to", to.String()),
				)

				if to == gobreaker.StateOpen {
					b.metrics.mutex.Lock()
					b.metrics.CircuitBreakerTrips++
					b.metrics.mutex.Unlock()
				}
			},
		}

		b.circuitBreakers[service] = gobreaker.NewCircuitBreaker(settings)
	}
}

// buildHealthCheckEndpoints creates health check endpoint map
func (b *EnhancedIntegrationBridge) buildHealthCheckEndpoints() map[string]string {
	baseURL := b.config.PythonMixerBaseURL

	return map[string]string{
		"orchestrator":     fmt.Sprintf("%s:%d/health", baseURL, b.config.OrchestratorPort),
		"email_processing": fmt.Sprintf("%s:%d/health", baseURL, b.config.EmailProcessingPort),
		"transcription":    fmt.Sprintf("%s:%d/health", baseURL, b.config.TranscriptionPort),
		"telegram_bot":     fmt.Sprintf("%s:%d/health", baseURL, b.config.TelegramBotPort),
	}
}

// performInitialHealthCheck performs initial health check on all services
func (b *EnhancedIntegrationBridge) performInitialHealthCheck() error {
	b.logger.Info("🏥 Performing initial health check...")

	var errors []string
	for service, endpoint := range b.healthChecker.endpoints {
		result := b.checkServiceHealth(service, endpoint)

		b.healthChecker.mutex.Lock()
		b.healthChecker.results[service] = result
		b.healthChecker.mutex.Unlock()

		if result.Status != "healthy" {
			errors = append(errors, fmt.Sprintf("%s: %s", service, result.Error))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("health check failures: %v", errors)
	}

	b.logger.Info("✅ Initial health check passed")
	return nil
}

// checkServiceHealth checks health of a specific service
func (b *EnhancedIntegrationBridge) checkServiceHealth(service, endpoint string) *HealthResult {
	start := time.Now()
	result := &HealthResult{
		Endpoint:  endpoint,
		LastCheck: start,
	}

	ctx, cancel := context.WithTimeout(b.ctx, b.config.HealthCheckTimeout)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", endpoint, nil)
	if err != nil {
		result.Status = "error"
		result.Error = fmt.Sprintf("failed to create request: %v", err)
		return result
	}

	resp, err := b.httpClient.Do(req)
	if err != nil {
		result.Status = "error"
		result.Error = fmt.Sprintf("request failed: %v", err)
		result.ResponseTime = time.Since(start)
		return result
	}
	defer resp.Body.Close()

	result.ResponseTime = time.Since(start)

	if resp.StatusCode == http.StatusOK {
		result.Status = "healthy"
	} else {
		result.Status = "unhealthy"
		result.Error = fmt.Sprintf("HTTP %d", resp.StatusCode)
	}

	return result
}

// messageProcessor processes messages from queues
func (b *EnhancedIntegrationBridge) messageProcessor(workerID int) {
	defer b.wg.Done()

	b.logger.Infof("🔄 Message processor %d started", workerID)

	for {
		select {
		case <-b.ctx.Done():
			b.logger.Infof("🔄 Message processor %d shutting down", workerID)
			return

		case msg := <-b.incomingQueue:
			b.processIncomingMessage(msg)

		case msg := <-b.outgoingQueue:
			b.processOutgoingMessage(msg)
		}
	}
}

// processIncomingMessage processes incoming messages from python_mixer
func (b *EnhancedIntegrationBridge) processIncomingMessage(msg *BridgeMessage) {
	start := time.Now()

	b.zapLogger.Debug("Processing incoming message",
		zap.String("id", msg.ID),
		zap.String("type", msg.Type),
		zap.String("source", msg.Source),
	)

	defer func() {
		processingTime := time.Since(start).Seconds()

		b.metrics.mutex.Lock()
		b.metrics.MessagesReceived++
		b.metrics.TotalProcessingTime += processingTime
		b.metrics.AverageResponseTime = b.metrics.TotalProcessingTime / float64(b.metrics.MessagesReceived)
		b.metrics.mutex.Unlock()
	}()

	// Route message based on type
	switch msg.Type {
	case "transcription_completed":
		b.handleTranscriptionCompleted(msg)
	case "email_processed":
		b.handleEmailProcessed(msg)
	case "telegram_message":
		b.handleTelegramMessage(msg)
	case "health_status":
		b.handleHealthStatus(msg)
	default:
		b.logger.Warnf("Unknown message type: %s", msg.Type)
		b.metrics.mutex.Lock()
		b.metrics.MessagesFailed++
		b.metrics.mutex.Unlock()
	}
}

// processOutgoingMessage processes outgoing messages to python_mixer
func (b *EnhancedIntegrationBridge) processOutgoingMessage(msg *BridgeMessage) {
	start := time.Now()

	b.zapLogger.Debug("Processing outgoing message",
		zap.String("id", msg.ID),
		zap.String("type", msg.Type),
		zap.String("destination", msg.Destination),
	)

	// Determine target service
	service := b.getServiceFromDestination(msg.Destination)
	if service == "" {
		b.logger.Errorf("Unknown destination: %s", msg.Destination)
		return
	}

	// Execute with circuit breaker
	_, err := b.circuitBreakers[service].Execute(func() (interface{}, error) {
		return nil, b.sendMessageToService(service, msg)
	})

	processingTime := time.Since(start).Seconds()

	b.metrics.mutex.Lock()
	if err != nil {
		b.metrics.MessagesFailed++
		if err == gobreaker.ErrOpenState {
			b.metrics.CircuitBreakerTrips++
		}
	} else {
		b.metrics.MessagesSent++
		b.metrics.MessagesProcessed++
	}
	b.metrics.TotalProcessingTime += processingTime
	b.metrics.AverageResponseTime = b.metrics.TotalProcessingTime / float64(b.metrics.MessagesProcessed)
	b.metrics.mutex.Unlock()

	if err != nil {
		b.logger.Errorf("Failed to send message to %s: %v", service, err)

		// Retry logic for failed messages
		if msg.RetryCount < b.config.MaxRetries {
			msg.RetryCount++
			delay := time.Duration(float64(b.config.RetryDelay) *
				(b.config.RetryBackoffFactor * float64(msg.RetryCount)))

			time.AfterFunc(delay, func() {
				select {
				case b.outgoingQueue <- msg:
					b.metrics.mutex.Lock()
					b.metrics.RetryAttempts++
					b.metrics.mutex.Unlock()
				default:
					b.logger.Warn("Outgoing queue full, dropping retry message")
				}
			})
		}
	}
}

// healthMonitor continuously monitors service health
func (b *EnhancedIntegrationBridge) healthMonitor() {
	defer b.wg.Done()

	b.logger.Info("🏥 Health monitor started")
	ticker := time.NewTicker(b.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-b.ctx.Done():
			b.logger.Info("🏥 Health monitor shutting down")
			return

		case <-ticker.C:
			b.performHealthCheck()
		}
	}
}

// performHealthCheck checks health of all services
func (b *EnhancedIntegrationBridge) performHealthCheck() {
	for service, endpoint := range b.healthChecker.endpoints {
		result := b.checkServiceHealth(service, endpoint)

		b.healthChecker.mutex.Lock()
		b.healthChecker.results[service] = result
		b.healthChecker.mutex.Unlock()

		if result.Status != "healthy" {
			b.metrics.mutex.Lock()
			b.metrics.HealthCheckFailures++
			b.metrics.mutex.Unlock()

			b.zapLogger.Warn("Service health check failed",
				zap.String("service", service),
				zap.String("endpoint", endpoint),
				zap.String("error", result.Error),
			)
		}
	}

	b.metrics.mutex.Lock()
	b.metrics.LastHealthCheck = time.Now()
	b.metrics.mutex.Unlock()
}

// metricsCollector collects and reports metrics
func (b *EnhancedIntegrationBridge) metricsCollector() {
	defer b.wg.Done()

	b.logger.Info("📊 Metrics collector started")
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-b.ctx.Done():
			b.logger.Info("📊 Metrics collector shutting down")
			return

		case <-ticker.C:
			b.collectMetrics()
		}
	}
}

// collectMetrics collects current metrics
func (b *EnhancedIntegrationBridge) collectMetrics() {
	b.metrics.mutex.Lock()
	defer b.metrics.mutex.Unlock()

	// Update active connections count
	b.connectionMutex.RLock()
	b.metrics.ActiveConnections = int64(len(b.activeConnections))
	b.connectionMutex.RUnlock()

	// Log metrics periodically
	b.zapLogger.Info("Integration bridge metrics",
		zap.Int64("total_connections", b.metrics.TotalConnections),
		zap.Int64("active_connections", b.metrics.ActiveConnections),
		zap.Int64("messages_sent", b.metrics.MessagesSent),
		zap.Int64("messages_received", b.metrics.MessagesReceived),
		zap.Int64("messages_failed", b.metrics.MessagesFailed),
		zap.Float64("avg_response_time", b.metrics.AverageResponseTime),
		zap.Int64("circuit_breaker_trips", b.metrics.CircuitBreakerTrips),
	)
}

// getServiceFromDestination maps destination to service name
func (b *EnhancedIntegrationBridge) getServiceFromDestination(destination string) string {
	switch destination {
	case "python_mixer_orchestrator":
		return "orchestrator"
	case "python_mixer_email":
		return "email_processing"
	case "python_mixer_transcription":
		return "transcription"
	case "python_mixer_telegram":
		return "telegram_bot"
	default:
		return ""
	}
}

// sendMessageToService sends a message to a specific python_mixer service
func (b *EnhancedIntegrationBridge) sendMessageToService(service string, msg *BridgeMessage) error {
	var endpoint string
	baseURL := b.config.PythonMixerBaseURL

	switch service {
	case "orchestrator":
		endpoint = fmt.Sprintf("%s:%d/api/message", baseURL, b.config.OrchestratorPort)
	case "email_processing":
		endpoint = fmt.Sprintf("%s:%d/api/message", baseURL, b.config.EmailProcessingPort)
	case "transcription":
		endpoint = fmt.Sprintf("%s:%d/api/message", baseURL, b.config.TranscriptionPort)
	case "telegram_bot":
		endpoint = fmt.Sprintf("%s:%d/api/message", baseURL, b.config.TelegramBotPort)
	default:
		return fmt.Errorf("unknown service: %s", service)
	}

	jsonData, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	ctx, cancel := context.WithTimeout(b.ctx, b.config.ConnectionTimeout)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "POST", endpoint,
		bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := b.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP %d", resp.StatusCode)
	}

	return nil
}

// Message handlers for different types of incoming messages

// handleTranscriptionCompleted processes completed transcription messages
func (b *EnhancedIntegrationBridge) handleTranscriptionCompleted(msg *BridgeMessage) {
	b.zapLogger.Info("Processing transcription completed message",
		zap.String("message_id", msg.ID),
		zap.Any("data", msg.Data),
	)

	// Extract transcription data
	transcriptionData, ok := msg.Data["transcription"].(map[string]interface{})
	if !ok {
		b.logger.Error("Invalid transcription data format")
		return
	}

	// Forward to HVAC CRM system for processing
	// This would typically involve:
	// 1. Creating customer records
	// 2. Creating service tickets
	// 3. Updating equipment registry
	// 4. Triggering workflow automation

	b.logger.Infof("Transcription processed for email: %v", transcriptionData["email_id"])
}

// handleEmailProcessed processes email processing completion messages
func (b *EnhancedIntegrationBridge) handleEmailProcessed(msg *BridgeMessage) {
	b.zapLogger.Info("Processing email processed message",
		zap.String("message_id", msg.ID),
		zap.Any("data", msg.Data),
	)

	// Extract email metadata
	emailData, ok := msg.Data["email"].(map[string]interface{})
	if !ok {
		b.logger.Error("Invalid email data format")
		return
	}

	// Update email processing status in CRM
	b.logger.Infof("Email processed: %v", emailData["email_id"])
}

// handleTelegramMessage processes Telegram bot messages
func (b *EnhancedIntegrationBridge) handleTelegramMessage(msg *BridgeMessage) {
	b.zapLogger.Info("Processing Telegram message",
		zap.String("message_id", msg.ID),
		zap.Any("data", msg.Data),
	)

	// Extract Telegram message data
	telegramData, ok := msg.Data["telegram"].(map[string]interface{})
	if !ok {
		b.logger.Error("Invalid Telegram data format")
		return
	}

	// Process Telegram interactions
	// This could involve:
	// 1. Customer inquiries
	// 2. Service requests
	// 3. Status updates
	// 4. Equipment monitoring alerts

	b.logger.Infof("Telegram message processed: %v", telegramData["message_id"])
}

// handleHealthStatus processes health status updates from python_mixer services
func (b *EnhancedIntegrationBridge) handleHealthStatus(msg *BridgeMessage) {
	b.zapLogger.Debug("Processing health status message",
		zap.String("message_id", msg.ID),
		zap.Any("data", msg.Data),
	)

	// Extract health data
	healthData, ok := msg.Data["health"].(map[string]interface{})
	if !ok {
		b.logger.Error("Invalid health data format")
		return
	}

	// Update service health status
	serviceName, _ := healthData["service"].(string)
	status, _ := healthData["status"].(string)

	if serviceName != "" && status != "" {
		b.healthChecker.mutex.Lock()
		if result, exists := b.healthChecker.results[serviceName]; exists {
			result.Status = status
			result.LastCheck = time.Now()
		}
		b.healthChecker.mutex.Unlock()

		b.logger.Infof("Health status updated for %s: %s", serviceName, status)
	}
}

// Public API methods for external integration

// SendTranscriptionRequest sends a transcription request to python_mixer
func (b *EnhancedIntegrationBridge) SendTranscriptionRequest(emailID, filePath string, metadata map[string]interface{}) error {
	msg := &BridgeMessage{
		ID:          fmt.Sprintf("transcription_%s_%d", emailID, time.Now().UnixNano()),
		Type:        "transcription_request",
		Source:      "gobackend_crm",
		Destination: "python_mixer_transcription",
		Data: map[string]interface{}{
			"email_id":  emailID,
			"file_path": filePath,
			"metadata":  metadata,
		},
		Timestamp: time.Now(),
		Priority:  PriorityHigh,
		Metadata: map[string]string{
			"request_type": "m4a_transcription",
			"language":     "pl",
		},
	}

	select {
	case b.outgoingQueue <- msg:
		return nil
	default:
		return fmt.Errorf("outgoing queue full")
	}
}

// SendEmailAnalysisRequest sends an email analysis request to python_mixer
func (b *EnhancedIntegrationBridge) SendEmailAnalysisRequest(emailData map[string]interface{}) error {
	msg := &BridgeMessage{
		ID:          fmt.Sprintf("email_analysis_%d", time.Now().UnixNano()),
		Type:        "email_analysis_request",
		Source:      "gobackend_crm",
		Destination: "python_mixer_email",
		Data:        emailData,
		Timestamp:   time.Now(),
		Priority:    PriorityNormal,
		Metadata: map[string]string{
			"request_type": "hvac_analysis",
		},
	}

	select {
	case b.outgoingQueue <- msg:
		return nil
	default:
		return fmt.Errorf("outgoing queue full")
	}
}

// GetBridgeMetrics returns current bridge metrics
func (b *EnhancedIntegrationBridge) GetBridgeMetrics() *BridgeMetrics {
	b.metrics.mutex.RLock()
	defer b.metrics.mutex.RUnlock()

	// Return a copy of metrics
	return &BridgeMetrics{
		TotalConnections:    b.metrics.TotalConnections,
		ActiveConnections:   b.metrics.ActiveConnections,
		FailedConnections:   b.metrics.FailedConnections,
		MessagesSent:        b.metrics.MessagesSent,
		MessagesReceived:    b.metrics.MessagesReceived,
		MessagesProcessed:   b.metrics.MessagesProcessed,
		MessagesFailed:      b.metrics.MessagesFailed,
		AverageResponseTime: b.metrics.AverageResponseTime,
		TotalProcessingTime: b.metrics.TotalProcessingTime,
		CircuitBreakerTrips: b.metrics.CircuitBreakerTrips,
		RetryAttempts:       b.metrics.RetryAttempts,
		TimeoutErrors:       b.metrics.TimeoutErrors,
		LastHealthCheck:     b.metrics.LastHealthCheck,
		HealthCheckFailures: b.metrics.HealthCheckFailures,
	}
}

// GetHealthStatus returns current health status of all services
func (b *EnhancedIntegrationBridge) GetHealthStatus() map[string]*HealthResult {
	b.healthChecker.mutex.RLock()
	defer b.healthChecker.mutex.RUnlock()

	// Return a copy of health results
	results := make(map[string]*HealthResult)
	for service, result := range b.healthChecker.results {
		results[service] = &HealthResult{
			Endpoint:     result.Endpoint,
			Status:       result.Status,
			ResponseTime: result.ResponseTime,
			LastCheck:    result.LastCheck,
			Error:        result.Error,
		}
	}

	return results
}
