package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"go.uber.org/zap"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/integration"
)

// 🌉 Integration Service - Manages Python Mixer Integration
// Provides high-level integration services for the HVAC CRM system

type IntegrationService struct {
	// Core components
	bridge         *integration.EnhancedIntegrationBridge
	customerUc     *biz.CustomerUsecase
	emailUc        *biz.EmailUsecase
	transcriptionUc *biz.TranscriptionUsecase
	logger         *log.Helper
	zapLogger      *zap.Logger

	// Configuration
	config *IntegrationConfig
}

type IntegrationConfig struct {
	// Python Mixer configuration
	PythonMixerBaseURL     string        `json:"python_mixer_base_url"`
	OrchestratorPort       int           `json:"orchestrator_port"`
	EmailProcessingPort    int           `json:"email_processing_port"`
	TranscriptionPort      int           `json:"transcription_port"`
	TelegramBotPort        int           `json:"telegram_bot_port"`
	
	// Integration settings
	EnableAutoSync         bool          `json:"enable_auto_sync"`
	SyncInterval          time.Duration `json:"sync_interval"`
	EnableRealTimeEvents  bool          `json:"enable_real_time_events"`
	
	// Performance settings
	MaxConcurrentRequests int           `json:"max_concurrent_requests"`
	RequestTimeout        time.Duration `json:"request_timeout"`
	
	// Retry settings
	MaxRetries            int           `json:"max_retries"`
	RetryDelay           time.Duration `json:"retry_delay"`
}

// TranscriptionRequest represents a transcription request
type TranscriptionRequest struct {
	EmailID     string                 `json:"email_id"`
	FilePath    string                 `json:"file_path"`
	Language    string                 `json:"language"`
	Priority    string                 `json:"priority"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// TranscriptionResult represents a transcription result
type TranscriptionResult struct {
	EmailID        string                 `json:"email_id"`
	Transcript     string                 `json:"transcript"`
	Confidence     float64                `json:"confidence"`
	Language       string                 `json:"language"`
	ProcessingTime float64                `json:"processing_time"`
	Metadata       map[string]interface{} `json:"metadata"`
	Timestamp      time.Time              `json:"timestamp"`
}

// EmailAnalysisRequest represents an email analysis request
type EmailAnalysisRequest struct {
	EmailID   string                 `json:"email_id"`
	Subject   string                 `json:"subject"`
	Sender    string                 `json:"sender"`
	Recipient string                 `json:"recipient"`
	Body      string                 `json:"body"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// EmailAnalysisResult represents an email analysis result
type EmailAnalysisResult struct {
	EmailID           string                 `json:"email_id"`
	Category          string                 `json:"category"`
	Priority          string                 `json:"priority"`
	Sentiment         string                 `json:"sentiment"`
	HVACRelevance     float64                `json:"hvac_relevance"`
	ExtractedEntities map[string]interface{} `json:"extracted_entities"`
	RecommendedAction string                 `json:"recommended_action"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// IntegrationStatus represents the current integration status
type IntegrationStatus struct {
	IsHealthy           bool                           `json:"is_healthy"`
	ServicesStatus      map[string]*integration.HealthResult `json:"services_status"`
	Metrics            *integration.BridgeMetrics     `json:"metrics"`
	LastHealthCheck    time.Time                      `json:"last_health_check"`
	ActiveConnections  int                            `json:"active_connections"`
	TotalProcessed     int64                          `json:"total_processed"`
	ErrorRate          float64                        `json:"error_rate"`
}

// NewIntegrationService creates a new integration service
func NewIntegrationService(
	customerUc *biz.CustomerUsecase,
	emailUc *biz.EmailUsecase,
	transcriptionUc *biz.TranscriptionUsecase,
	config *IntegrationConfig,
	logger log.Logger,
	zapLogger *zap.Logger,
) (*IntegrationService, error) {
	
	// Create bridge configuration
	bridgeConfig := &integration.BridgeConfig{
		PythonMixerBaseURL:  config.PythonMixerBaseURL,
		OrchestratorPort:    config.OrchestratorPort,
		EmailProcessingPort: config.EmailProcessingPort,
		TranscriptionPort:   config.TranscriptionPort,
		TelegramBotPort:     config.TelegramBotPort,
		MaxConnections:      config.MaxConcurrentRequests,
		ConnectionTimeout:   config.RequestTimeout,
		ReadTimeout:         config.RequestTimeout,
		WriteTimeout:        config.RequestTimeout,
		MaxRetries:          config.MaxRetries,
		RetryDelay:          config.RetryDelay,
		RetryBackoffFactor:  2.0,
		HealthCheckInterval: 30 * time.Second,
		HealthCheckTimeout:  10 * time.Second,
		QueueSize:           1000,
		ProcessingWorkers:   4,
		CircuitBreakerSettings: map[string]*integration.CircuitBreakerConfig{
			"orchestrator": {
				MaxRequests:  3,
				Interval:     60 * time.Second,
				Timeout:      30 * time.Second,
				FailureRatio: 0.6,
				MinRequests:  3,
			},
			"email_processing": {
				MaxRequests:  5,
				Interval:     60 * time.Second,
				Timeout:      20 * time.Second,
				FailureRatio: 0.7,
				MinRequests:  3,
			},
			"transcription": {
				MaxRequests:  3,
				Interval:     120 * time.Second,
				Timeout:      60 * time.Second,
				FailureRatio: 0.5,
				MinRequests:  2,
			},
			"telegram_bot": {
				MaxRequests:  10,
				Interval:     60 * time.Second,
				Timeout:      15 * time.Second,
				FailureRatio: 0.8,
				MinRequests:  5,
			},
		},
	}

	// Create enhanced integration bridge
	bridge := integration.NewEnhancedIntegrationBridge(bridgeConfig, logger, zapLogger)

	service := &IntegrationService{
		bridge:          bridge,
		customerUc:      customerUc,
		emailUc:         emailUc,
		transcriptionUc: transcriptionUc,
		logger:          log.NewHelper(logger),
		zapLogger:       zapLogger,
		config:          config,
	}

	return service, nil
}

// Initialize starts the integration service
func (s *IntegrationService) Initialize(ctx context.Context) error {
	s.logger.Info("🌉 Initializing Integration Service...")

	// Initialize the bridge
	if err := s.bridge.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize integration bridge: %w", err)
	}

	s.logger.Info("✅ Integration Service initialized successfully")
	return nil
}

// Shutdown gracefully shuts down the integration service
func (s *IntegrationService) Shutdown(ctx context.Context) error {
	s.logger.Info("🔄 Shutting down Integration Service...")

	if err := s.bridge.Shutdown(); err != nil {
		return fmt.Errorf("failed to shutdown integration bridge: %w", err)
	}

	s.logger.Info("✅ Integration Service shut down successfully")
	return nil
}

// RequestTranscription requests transcription of an audio file
func (s *IntegrationService) RequestTranscription(ctx context.Context, req *TranscriptionRequest) error {
	s.logger.Infof("📝 Requesting transcription for email %s", req.EmailID)

	metadata := req.Metadata
	if metadata == nil {
		metadata = make(map[string]interface{})
	}

	// Add additional metadata
	metadata["request_timestamp"] = time.Now().ISO8601()
	metadata["source"] = "gobackend_crm"
	metadata["priority"] = req.Priority

	err := s.bridge.SendTranscriptionRequest(req.EmailID, req.FilePath, metadata)
	if err != nil {
		s.logger.Errorf("Failed to send transcription request: %v", err)
		return fmt.Errorf("failed to send transcription request: %w", err)
	}

	s.logger.Infof("✅ Transcription request sent for email %s", req.EmailID)
	return nil
}

// RequestEmailAnalysis requests analysis of an email
func (s *IntegrationService) RequestEmailAnalysis(ctx context.Context, req *EmailAnalysisRequest) error {
	s.logger.Infof("📧 Requesting email analysis for email %s", req.EmailID)

	emailData := map[string]interface{}{
		"email_id":  req.EmailID,
		"subject":   req.Subject,
		"sender":    req.Sender,
		"recipient": req.Recipient,
		"body":      req.Body,
		"metadata":  req.Metadata,
		"timestamp": time.Now().ISO8601(),
	}

	err := s.bridge.SendEmailAnalysisRequest(emailData)
	if err != nil {
		s.logger.Errorf("Failed to send email analysis request: %v", err)
		return fmt.Errorf("failed to send email analysis request: %w", err)
	}

	s.logger.Infof("✅ Email analysis request sent for email %s", req.EmailID)
	return nil
}

// GetIntegrationStatus returns the current integration status
func (s *IntegrationService) GetIntegrationStatus(ctx context.Context) (*IntegrationStatus, error) {
	// Get bridge metrics
	metrics := s.bridge.GetBridgeMetrics()
	
	// Get health status
	healthStatus := s.bridge.GetHealthStatus()
	
	// Calculate overall health
	healthyServices := 0
	totalServices := len(healthStatus)
	
	for _, status := range healthStatus {
		if status.Status == "healthy" {
			healthyServices++
		}
	}
	
	isHealthy := healthyServices == totalServices
	
	// Calculate error rate
	var errorRate float64
	if metrics.MessagesSent > 0 {
		errorRate = float64(metrics.MessagesFailed) / float64(metrics.MessagesSent) * 100
	}

	status := &IntegrationStatus{
		IsHealthy:         isHealthy,
		ServicesStatus:    healthStatus,
		Metrics:          metrics,
		LastHealthCheck:  metrics.LastHealthCheck,
		ActiveConnections: int(metrics.ActiveConnections),
		TotalProcessed:   metrics.MessagesProcessed,
		ErrorRate:        errorRate,
	}

	return status, nil
}

// ProcessIncomingTranscription processes a transcription result from python_mixer
func (s *IntegrationService) ProcessIncomingTranscription(ctx context.Context, result *TranscriptionResult) error {
	s.logger.Infof("📝 Processing incoming transcription for email %s", result.EmailID)

	// Store transcription in database
	transcription := &biz.Transcription{
		EmailID:        result.EmailID,
		Transcript:     result.Transcript,
		Confidence:     result.Confidence,
		Language:       result.Language,
		ProcessingTime: result.ProcessingTime,
		Metadata:       result.Metadata,
		CreatedAt:      result.Timestamp,
	}

	if err := s.transcriptionUc.CreateTranscription(ctx, transcription); err != nil {
		s.logger.Errorf("Failed to store transcription: %v", err)
		return fmt.Errorf("failed to store transcription: %w", err)
	}

	// Trigger additional processing (customer creation, service tickets, etc.)
	if err := s.processTranscriptionWorkflow(ctx, result); err != nil {
		s.logger.Errorf("Failed to process transcription workflow: %v", err)
		// Don't return error here as transcription is already stored
	}

	s.logger.Infof("✅ Transcription processed successfully for email %s", result.EmailID)
	return nil
}

// ProcessIncomingEmailAnalysis processes an email analysis result from python_mixer
func (s *IntegrationService) ProcessIncomingEmailAnalysis(ctx context.Context, result *EmailAnalysisResult) error {
	s.logger.Infof("📧 Processing incoming email analysis for email %s", result.EmailID)

	// Store email analysis in database
	analysis := &biz.EmailAnalysis{
		EmailID:           result.EmailID,
		Category:          result.Category,
		Priority:          result.Priority,
		Sentiment:         result.Sentiment,
		HVACRelevance:     result.HVACRelevance,
		ExtractedEntities: result.ExtractedEntities,
		RecommendedAction: result.RecommendedAction,
		Metadata:          result.Metadata,
		CreatedAt:         time.Now(),
	}

	if err := s.emailUc.CreateEmailAnalysis(ctx, analysis); err != nil {
		s.logger.Errorf("Failed to store email analysis: %v", err)
		return fmt.Errorf("failed to store email analysis: %w", err)
	}

	// Trigger additional processing based on analysis
	if err := s.processEmailAnalysisWorkflow(ctx, result); err != nil {
		s.logger.Errorf("Failed to process email analysis workflow: %v", err)
		// Don't return error here as analysis is already stored
	}

	s.logger.Infof("✅ Email analysis processed successfully for email %s", result.EmailID)
	return nil
}

// processTranscriptionWorkflow handles the workflow after transcription processing
func (s *IntegrationService) processTranscriptionWorkflow(ctx context.Context, result *TranscriptionResult) error {
	// This would typically involve:
	// 1. Extracting customer information from transcription
	// 2. Creating or updating customer records
	// 3. Creating service tickets if needed
	// 4. Updating equipment registry
	// 5. Triggering notifications

	s.logger.Infof("🔄 Processing transcription workflow for email %s", result.EmailID)
	
	// Placeholder for workflow implementation
	// TODO: Implement actual workflow logic
	
	return nil
}

// processEmailAnalysisWorkflow handles the workflow after email analysis
func (s *IntegrationService) processEmailAnalysisWorkflow(ctx context.Context, result *EmailAnalysisResult) error {
	// This would typically involve:
	// 1. Creating leads based on email content
	// 2. Assigning priority based on analysis
	// 3. Routing to appropriate teams
	// 4. Triggering automated responses

	s.logger.Infof("🔄 Processing email analysis workflow for email %s", result.EmailID)
	
	// Placeholder for workflow implementation
	// TODO: Implement actual workflow logic
	
	return nil
}
