package server

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-kratos/kratos/v2/log"
	"go.uber.org/zap"

	"gobackend-hvac-kratos/internal/integration"
	"gobackend-hvac-kratos/internal/service"
)

// 🌉 Integration Handler - HTTP endpoints for Python Mixer integration
// Provides RESTful API for integration management and monitoring

type IntegrationHandler struct {
	integrationService *service.IntegrationService
	logger             *log.Helper
	zapLogger          *zap.Logger
}

// NewIntegrationHandler creates a new integration handler
func NewIntegrationHandler(
	integrationService *service.IntegrationService,
	logger log.Logger,
	zapLogger *zap.Logger,
) *IntegrationHandler {
	return &IntegrationHandler{
		integrationService: integrationService,
		logger:             log.NewHelper(logger),
		zapLogger:          zapLogger,
	}
}

// RegisterRoutes registers integration routes
func (h *IntegrationHandler) RegisterRoutes(router *gin.Engine) {
	integrationGroup := router.Group("/api/integration")
	{
		// Status and monitoring endpoints
		integrationGroup.GET("/status", h.GetIntegrationStatus)
		integrationGroup.GET("/health", h.GetHealthStatus)
		integrationGroup.GET("/metrics", h.GetMetrics)

		// Transcription endpoints
		integrationGroup.POST("/transcription/request", h.RequestTranscription)
		integrationGroup.POST("/transcription/result", h.ReceiveTranscriptionResult)

		// Email analysis endpoints
		integrationGroup.POST("/email/analysis/request", h.RequestEmailAnalysis)
		integrationGroup.POST("/email/analysis/result", h.ReceiveEmailAnalysisResult)

		// Message handling endpoints
		integrationGroup.POST("/message", h.ReceiveMessage)
		integrationGroup.GET("/message/status/:messageId", h.GetMessageStatus)

		// Administrative endpoints
		integrationGroup.POST("/sync/trigger", h.TriggerSync)
		integrationGroup.GET("/services", h.GetServicesStatus)
	}
}

// GetIntegrationStatus returns the current integration status
func (h *IntegrationHandler) GetIntegrationStatus(c *gin.Context) {
	h.zapLogger.Debug("Getting integration status")

	status, err := h.integrationService.GetIntegrationStatus(c.Request.Context())
	if err != nil {
		h.zapLogger.Error("Failed to get integration status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get integration status",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "success",
		"data":      status,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetHealthStatus returns health status of all services
func (h *IntegrationHandler) GetHealthStatus(c *gin.Context) {
	h.zapLogger.Debug("Getting health status")

	status, err := h.integrationService.GetIntegrationStatus(c.Request.Context())
	if err != nil {
		h.zapLogger.Error("Failed to get health status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get health status",
			"details": err.Error(),
		})
		return
	}

	healthResponse := gin.H{
		"status":             "success",
		"is_healthy":         status.IsHealthy,
		"services_status":    status.ServicesStatus,
		"last_check":         status.LastHealthCheck,
		"active_connections": status.ActiveConnections,
		"timestamp":          time.Now().Format(time.RFC3339),
	}

	if status.IsHealthy {
		c.JSON(http.StatusOK, healthResponse)
	} else {
		c.JSON(http.StatusServiceUnavailable, healthResponse)
	}
}

// GetMetrics returns integration metrics
func (h *IntegrationHandler) GetMetrics(c *gin.Context) {
	h.zapLogger.Debug("Getting integration metrics")

	status, err := h.integrationService.GetIntegrationStatus(c.Request.Context())
	if err != nil {
		h.zapLogger.Error("Failed to get metrics", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get metrics",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "success",
		"metrics":   status.Metrics,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// RequestTranscription handles transcription requests
func (h *IntegrationHandler) RequestTranscription(c *gin.Context) {
	h.zapLogger.Debug("Handling transcription request")

	var req service.TranscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.zapLogger.Error("Invalid transcription request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if req.EmailID == "" || req.FilePath == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing required fields: email_id and file_path are required",
		})
		return
	}

	// Set defaults
	if req.Language == "" {
		req.Language = "pl"
	}
	if req.Priority == "" {
		req.Priority = "normal"
	}

	err := h.integrationService.RequestTranscription(c.Request.Context(), &req)
	if err != nil {
		h.zapLogger.Error("Failed to request transcription", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to request transcription",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "success",
		"message":   "Transcription request submitted successfully",
		"email_id":  req.EmailID,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// ReceiveTranscriptionResult handles incoming transcription results
func (h *IntegrationHandler) ReceiveTranscriptionResult(c *gin.Context) {
	h.zapLogger.Debug("Receiving transcription result")

	var result service.TranscriptionResult
	if err := c.ShouldBindJSON(&result); err != nil {
		h.zapLogger.Error("Invalid transcription result", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid result format",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if result.EmailID == "" || result.Transcript == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing required fields: email_id and transcript are required",
		})
		return
	}

	err := h.integrationService.ProcessIncomingTranscription(c.Request.Context(), &result)
	if err != nil {
		h.zapLogger.Error("Failed to process transcription result", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to process transcription result",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "success",
		"message":   "Transcription result processed successfully",
		"email_id":  result.EmailID,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// RequestEmailAnalysis handles email analysis requests
func (h *IntegrationHandler) RequestEmailAnalysis(c *gin.Context) {
	h.zapLogger.Debug("Handling email analysis request")

	var req service.EmailAnalysisRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.zapLogger.Error("Invalid email analysis request", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if req.EmailID == "" || req.Subject == "" || req.Sender == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing required fields: email_id, subject, and sender are required",
		})
		return
	}

	err := h.integrationService.RequestEmailAnalysis(c.Request.Context(), &req)
	if err != nil {
		h.zapLogger.Error("Failed to request email analysis", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to request email analysis",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "success",
		"message":   "Email analysis request submitted successfully",
		"email_id":  req.EmailID,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// ReceiveEmailAnalysisResult handles incoming email analysis results
func (h *IntegrationHandler) ReceiveEmailAnalysisResult(c *gin.Context) {
	h.zapLogger.Debug("Receiving email analysis result")

	var result service.EmailAnalysisResult
	if err := c.ShouldBindJSON(&result); err != nil {
		h.zapLogger.Error("Invalid email analysis result", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid result format",
			"details": err.Error(),
		})
		return
	}

	// Validate required fields
	if result.EmailID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Missing required field: email_id is required",
		})
		return
	}

	err := h.integrationService.ProcessIncomingEmailAnalysis(c.Request.Context(), &result)
	if err != nil {
		h.zapLogger.Error("Failed to process email analysis result", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to process email analysis result",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":    "success",
		"message":   "Email analysis result processed successfully",
		"email_id":  result.EmailID,
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// ReceiveMessage handles generic messages from python_mixer
func (h *IntegrationHandler) ReceiveMessage(c *gin.Context) {
	h.zapLogger.Debug("Receiving generic message")

	var message map[string]interface{}
	if err := c.ShouldBindJSON(&message); err != nil {
		h.zapLogger.Error("Invalid message format", zap.Error(err))
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid message format",
			"details": err.Error(),
		})
		return
	}

	// Log the received message
	h.zapLogger.Info("Received message from python_mixer",
		zap.String("type", getStringFromMap(message, "type")),
		zap.String("source", getStringFromMap(message, "source")),
		zap.Any("data", message),
	)

	// Process based on message type
	messageType := getStringFromMap(message, "type")
	switch messageType {
	case "transcription_completed":
		// Handle transcription completion
		h.handleTranscriptionCompleted(c, message)
	case "email_processed":
		// Handle email processing completion
		h.handleEmailProcessed(c, message)
	case "health_status":
		// Handle health status update
		h.handleHealthStatusUpdate(c, message)
	default:
		h.zapLogger.Warn("Unknown message type", zap.String("type", messageType))
		c.JSON(http.StatusOK, gin.H{
			"status":  "received",
			"message": "Message received but not processed (unknown type)",
			"type":    messageType,
		})
		return
	}
}

// GetMessageStatus returns the status of a specific message
func (h *IntegrationHandler) GetMessageStatus(c *gin.Context) {
	messageID := c.Param("messageId")
	if messageID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Message ID is required",
		})
		return
	}

	// TODO: Implement message status tracking
	c.JSON(http.StatusOK, gin.H{
		"status":     "success",
		"message_id": messageID,
		"state":      "processed", // Placeholder
		"timestamp":  time.Now().Format(time.RFC3339),
	})
}

// TriggerSync triggers manual synchronization
func (h *IntegrationHandler) TriggerSync(c *gin.Context) {
	h.zapLogger.Info("Manual sync triggered")

	// TODO: Implement manual sync trigger
	c.JSON(http.StatusOK, gin.H{
		"status":    "success",
		"message":   "Synchronization triggered successfully",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// GetServicesStatus returns status of all python_mixer services
func (h *IntegrationHandler) GetServicesStatus(c *gin.Context) {
	h.zapLogger.Debug("Getting services status")

	status, err := h.integrationService.GetIntegrationStatus(c.Request.Context())
	if err != nil {
		h.zapLogger.Error("Failed to get services status", zap.Error(err))
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get services status",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":   "success",
		"services": status.ServicesStatus,
		"summary": gin.H{
			"total_services":   len(status.ServicesStatus),
			"healthy_services": countHealthyServices(status.ServicesStatus),
			"is_healthy":       status.IsHealthy,
		},
		"timestamp": time.Now().Format(time.RFC3339),
	})
}

// Helper methods

func (h *IntegrationHandler) handleTranscriptionCompleted(c *gin.Context, message map[string]interface{}) {
	// Extract transcription data and process
	h.zapLogger.Info("Processing transcription completed message")

	c.JSON(http.StatusOK, gin.H{
		"status":  "processed",
		"message": "Transcription completion processed",
		"type":    "transcription_completed",
	})
}

func (h *IntegrationHandler) handleEmailProcessed(c *gin.Context, message map[string]interface{}) {
	// Extract email data and process
	h.zapLogger.Info("Processing email processed message")

	c.JSON(http.StatusOK, gin.H{
		"status":  "processed",
		"message": "Email processing completion processed",
		"type":    "email_processed",
	})
}

func (h *IntegrationHandler) handleHealthStatusUpdate(c *gin.Context, message map[string]interface{}) {
	// Update health status
	h.zapLogger.Debug("Processing health status update")

	c.JSON(http.StatusOK, gin.H{
		"status":  "processed",
		"message": "Health status update processed",
		"type":    "health_status",
	})
}

func getStringFromMap(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

func countHealthyServices(services map[string]*integration.HealthResult) int {
	count := 0
	for _, service := range services {
		if service.Status == "healthy" {
			count++
		}
	}
	return count
}
