# HVAC-Remix Integration Configuration
integration:
  hvac_remix:
    enabled: true
    frontend_url: "http://localhost:3000"
    api_prefix: "/api/gobackend"
    websocket_path: "/ws"

  # 🌉 Enhanced Python Mixer Integration
  python_mixer:
    enabled: true
    base_url: "http://localhost"
    orchestrator_port: 9000
    email_processing_port: 8082
    transcription_port: 8889
    telegram_bot_port: 8083

    # Integration behavior settings
    behavior:
      enable_auto_sync: true
      sync_interval: "30s"
      enable_real_time_events: true
      enable_health_monitoring: true

    # Performance settings
    performance:
      max_concurrent_requests: 50
      request_timeout: "30s"
      connection_pool_size: 20
      idle_connection_timeout: "60s"

    # Retry configuration
    retry:
      max_retries: 3
      retry_delay: "2s"
      retry_backoff_factor: 2.0
      retry_jitter: true

    # Circuit breaker configurations per service
    circuit_breakers:
      orchestrator:
        max_requests: 5
        interval: "60s"
        timeout: "30s"
        failure_ratio: 0.6
        min_requests: 3

      email_processing:
        max_requests: 10
        interval: "60s"
        timeout: "20s"
        failure_ratio: 0.7
        min_requests: 5

      transcription:
        max_requests: 3
        interval: "120s"
        timeout: "60s"
        failure_ratio: 0.5
        min_requests: 2

      telegram_bot:
        max_requests: 15
        interval: "60s"
        timeout: "15s"
        failure_ratio: 0.8
        min_requests: 5

    # Health check settings
    health_check:
      interval: "30s"
      timeout: "10s"
      failure_threshold: 3
      recovery_threshold: 2

    # Message queue settings
    message_queue:
      queue_size: 1000
      processing_workers: 4
      priority_queue_enabled: true

    # Data validation settings
    validation:
      enable_strict_validation: true
      enable_schema_validation: true
      max_message_size: "10MB"
      allowed_message_types:
        - "transcription_request"
        - "transcription_completed"
        - "email_analysis_request"
        - "email_processed"
        - "telegram_message"
        - "health_status"
        - "sync_request"

    # Integration workflows
    workflows:
      transcription:
        auto_create_customers: true
        auto_create_service_tickets: true
        auto_update_equipment_registry: true
        enable_ai_analysis: true

      email_processing:
        auto_categorize: true
        auto_prioritize: true
        auto_route: true
        enable_sentiment_analysis: true

      telegram:
        auto_respond: false
        enable_command_processing: true
        enable_file_processing: true

  cors:
    allowed_origins:
      - "http://localhost:3000"
      - "https://hvac-remix.app"
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Content-Type", "Authorization", "X-Requested-With"]

  trpc:
    enabled: true
    endpoint: "/api/trpc"
    batch_requests: true
    max_batch_size: 10

# AI Services
ai:
  lm_studio:
    endpoint: "http://************:1234"
    model: "gemma-3-4b-it-qat"
    max_tokens: 4096
    temperature: 0.7
    timeout: 30s
    
  copilotkit_bridge:
    enabled: true
    streaming: true
    context_window: 32000

# Real-time Features
realtime:
  websocket:
    enabled: true
    port: 8080
    path: "/ws"
    ping_interval: 30s
    
  notifications:
    job_updates: true
    system_alerts: true
    ai_analysis: true

# Monitoring
monitoring:
  metrics:
    enabled: true
    endpoint: "/metrics"
    
  health:
    enabled: true
    endpoint: "/health"
    
  tracing:
    enabled: true
    jaeger_endpoint: "http://localhost:14268/api/traces"
